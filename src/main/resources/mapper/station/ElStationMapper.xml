<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yf.exam.modules.station.mapper.ElStationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yf.exam.modules.station.entity.ElStation">
        <id column="id" property="id" />
        <result column="admin_code_chn" property="adminCodeChn" />
        <result column="alti" property="alti" />
        <result column="city" property="city" />
        <result column="cnty" property="cnty" />
        <result column="lat" property="lat" />
        <result column="lon" property="lon" />
        <result column="station_id_c" property="stationIdC" />
        <result column="station_levl" property="stationLevl" />
        <result column="station_name" property="stationName" />
        <result column="alias" property="alias" />
        <result column="online" property="online" />
        <result column="draw_town" property="drawTown" />
        <result column="sort" property="sort" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`, `admin_code_chn`, `alti`, `city`, `cnty`, `lat`, `lon`, 
        `station_id_c`, `station_levl`, `station_name`, `alias`, 
        `online`, `draw_town`, `sort`
    </sql>

    <!-- 分页查询结果映射 -->
    <resultMap id="ListResultMap" 
               type="com.yf.exam.modules.station.dto.response.ElStationRespDTO" 
               extends="BaseResultMap">
    </resultMap>

    <!-- 分页查询 -->
    <select id="paging" resultMap="ListResultMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM el_station station
        <where>
            <if test="query != null">
                <if test="query.adminCodeChn != null and query.adminCodeChn != ''">
                    AND station.admin_code_chn LIKE CONCAT('%', #{query.adminCodeChn}, '%')
                </if>
                <if test="query.stationLevl != null and query.stationLevl != ''">
                    AND station.station_levl = #{query.stationLevl}
                </if>
                <if test="query.stationName != null and query.stationName != ''">
                    AND station.station_name LIKE CONCAT('%', #{query.stationName}, '%')
                </if>
                <if test="query.city != null and query.city != ''">
                    AND station.city LIKE CONCAT('%', #{query.city}, '%')
                </if>
                <if test="query.cnty != null and query.cnty != ''">
                    AND station.cnty LIKE CONCAT('%', #{query.cnty}, '%')
                </if>
                <if test="query.online != null">
                    AND station.online = #{query.online}
                </if>
                <if test="query.drawTown != null">
                    AND station.draw_town = #{query.drawTown}
                </if>
            </if>
        </where>
        ORDER BY station.sort ASC, station.id ASC
    </select>

    <!-- 根据区域代码数组查询站点信息 -->
    <select id="listByRegionCodes" resultMap="ListResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM el_station station
        <where>
            <if test="regionCodes != null and regionCodes.size() > 0">
                AND (
                <foreach collection="regionCodes" item="regionCode" separator=" OR ">
                    station.admin_code_chn LIKE #{regionCode}
                </foreach>
                )
            </if>
            <if test="query != null">
                <if test="query.stationLevl != null and query.stationLevl != ''">
                    AND station.station_levl in #{query.stationLevl}
                </if>
                <if test="query.online != null">
                    AND station.online = #{query.online}
                </if>
                <if test="query.drawTown != null">
                    AND station.draw_town = #{query.drawTown}
                </if>
            </if>
        </where>
        ORDER BY station.sort ASC, station.id ASC
    </select>

</mapper>
