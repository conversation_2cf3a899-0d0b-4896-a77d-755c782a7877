package com.yf.exam.modules.station.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yf.exam.core.api.dto.PagingReqDTO;
import com.yf.exam.core.utils.BeanMapper;
import com.yf.exam.core.utils.StringUtils;
import com.yf.exam.modules.station.dto.ElStationDTO;
import com.yf.exam.modules.station.dto.request.ElStationReqDTO;
import com.yf.exam.modules.station.dto.request.ElStationRegionReqDTO;
import com.yf.exam.modules.station.dto.response.ElStationRespDTO;
import com.yf.exam.modules.station.entity.ElStation;
import com.yf.exam.modules.station.mapper.ElStationMapper;
import com.yf.exam.modules.station.service.ElStationService;
import com.yf.exam.modules.station.utils.RegionCodeMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* <p>
* 气象站点 服务实现类
* </p>
*
* <AUTHOR>
* @since 2025-01-23
*/
@Service
public class ElStationServiceImpl extends ServiceImpl<ElStationMapper, ElStation> implements ElStationService {

    @Override
    public IPage<ElStationRespDTO> paging(PagingReqDTO<ElStationReqDTO> reqDTO) {
        return baseMapper.paging(reqDTO.toPage(), reqDTO.getParams());
    }

    @Override
    public void save(ElStationDTO reqDTO) {
        // 复制参数
        ElStation entity = new ElStation();
        BeanMapper.copy(reqDTO, entity);
        this.saveOrUpdate(entity);
    }

    @Override
    public List<ElStationRespDTO> listByRegionCodes(ElStationRegionReqDTO reqDTO) {
        if (reqDTO == null || StringUtils.isBlank(reqDTO.getRegionCode())) {
            return new ArrayList<>();
        }

        // 1. 将区域编号（1-9）转换为省份行政区划代码
        List<String> provinceCodes = RegionCodeMapper.convertRegionCodesToProvinceCodes(reqDTO.getRegionCode());

        if (provinceCodes.isEmpty()) {
            return new ArrayList<>();
        }

        // 2. 处理省份代码：将00替换为空，然后添加%进行模糊查询
        List<String> processedCodes = new ArrayList<>();
        for (String provinceCode : provinceCodes) {
            if (provinceCode != null && !provinceCode.trim().isEmpty()) {
                // 将00替换为空（如150000 -> 15）
                String processedCode = provinceCode.replace("00", "");
                // 添加%用于模糊查询（如15 -> 15%）
                processedCodes.add(processedCode + "%");
            }
        }

        if (processedCodes.isEmpty()) {
            return new ArrayList<>();
        }

        // 3. 调用Mapper进行查询
        return baseMapper.listByRegionCodes(processedCodes, reqDTO);
    }

    @Override
    public List<ElStationRespDTO> listByRegionCodes(String regionCode) {
        if (StringUtils.isBlank(regionCode)) {
            return new ArrayList<>();
        }

        // 创建请求DTO
        ElStationRegionReqDTO reqDTO = new ElStationRegionReqDTO();
        reqDTO.setRegionCode(regionCode);
        reqDTO.setOnline(1); // 默认查询在线站点
        reqDTO.setDrawTown(1); // 默认查询可绘制到地图的站点

        // 调用现有方法
        return listByRegionCodes(reqDTO);
    }
}
