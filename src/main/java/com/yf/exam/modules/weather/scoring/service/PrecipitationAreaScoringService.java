package com.yf.exam.modules.weather.scoring.service;

import com.yf.exam.modules.station.service.ElStationService;
import com.yf.exam.modules.station.dto.response.ElStationRespDTO;
import com.yf.exam.modules.station.dto.request.ElStationRegionReqDTO;
import com.yf.exam.modules.weather.micaps.MicapsData;
import com.yf.exam.modules.weather.micaps.MicapsDataService;
import com.yf.exam.modules.weather.micaps.MicapsType1Data;
import com.yf.exam.modules.weather.micaps.MicapsType3Data;
import com.yf.exam.modules.weather.micaps.MicapsType4Data;
import com.yf.exam.modules.weather.micaps.MicapsStation;
import com.yf.exam.modules.weather.scoring.dto.PrecipitationScoringResult;
import com.yf.exam.modules.weather.scoring.dto.StationPrecipitationData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 历史个例降水落区评分服务
 * 
 * 根据评分细节.md中的历史个例降水落区评分细节实现具体业务：
 * 1. 解析实况降水文件（MICAPS第一类文件）
 * 2. 解析CMA-MESO文件（MICAPS第四类文件）
 * 3. 解析考生答案（JSON降水落区）
 * 4. 计算晴雨TS评分和降水分级TS评分
 * 5. 计算技巧评分和最终评分
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */
@Slf4j
@Service
public class PrecipitationAreaScoringService {

    @Autowired
    private MicapsDataService micapsDataService;

    @Autowired
    private ElStationService stationService;

    /**
     * 计算降水落区评分（向后兼容版本）
     *
     * @param actualFilePath 实况降水文件路径（MICAPS第一类文件）
     * @param cmaMesoFilePath CMA-MESO文件路径（MICAPS第四类文件）
     * @param studentAnswer 考生答案（JSON降水落区）
     * @return 评分结果
     */
    public PrecipitationScoringResult calculatePrecipitationScore(
            String actualFilePath,
            String cmaMesoFilePath,
            Map<String, Object> studentAnswer) {
        return calculatePrecipitationScore(actualFilePath, cmaMesoFilePath, studentAnswer, null);
    }

    /**
     * 计算降水落区评分（带区域筛选）
     *
     * @param actualFilePath 实况降水文件路径（MICAPS第一类文件）
     * @param cmaMesoFilePath CMA-MESO文件路径（MICAPS第四类文件）
     * @param studentAnswer 考生答案（JSON降水落区）
     * @param regionCode 区域编码（1-9），用于筛选对应区域的站点
     * @return 评分结果
     */
    public PrecipitationScoringResult calculatePrecipitationScore(
            String actualFilePath,
            String cmaMesoFilePath,
            Map<String, Object> studentAnswer,
            String regionCode) {
        
        log.info("开始计算降水落区评分，实况文件：{}，CMA文件：{}", actualFilePath, cmaMesoFilePath);
        
        try {
            // 1. 解析实况降水数据（MICAPS第一类文件）
            List<StationPrecipitationData> actualData = parseActualPrecipitationData(actualFilePath);
            log.info("解析实况降水数据完成，站点数：{}", actualData.size());

            // 2. 如果提供了区域编码，则筛选对应区域的站点
            if (regionCode != null && !regionCode.trim().isEmpty()) {
                actualData = filterStationsByRegion(actualData, regionCode);
                log.info("根据区域编码{}筛选后，站点数：{}", regionCode, actualData.size());
            }

            // 3. 解析CMA-MESO预报数据（MICAPS第四类文件）
            List<StationPrecipitationData> cmaMesoData = parseCmaMesoData(cmaMesoFilePath, actualData);
            log.info("解析CMA-MESO数据完成，站点数：{}", cmaMesoData.size());

            // 4. 解析考生答案数据
            List<StationPrecipitationData> studentData = parseStudentAnswer(studentAnswer, actualData);
            log.info("解析考生答案完成，站点数：{}", studentData.size());

            // 5. 计算各量级TS评分
            Map<String, Double> studentTSScores = calculateAllLevelTS(studentData, actualData);
            Map<String, Double> cmaMesoTSScores = calculateAllLevelTS(cmaMesoData, actualData);
            log.info("TS评分计算完成，学生：{}，CMA-MESO：{}", studentTSScores, cmaMesoTSScores);

            // 5. 计算基础分
            Map<String, Double> baseScores = calculateBaseScores(studentTSScores, cmaMesoTSScores);

            // 6. 计算技巧评分
            Map<String, Double> skillScores = calculateSkillScores(studentTSScores, baseScores);

            // 7. 计算最终评分
            double finalScore = calculateWeightedFinalScore(skillScores);

            // 8. 构建评分结果
            PrecipitationScoringResult result = new PrecipitationScoringResult();
            result.setStudentTSScores(studentTSScores);
            result.setCmaMesoTSScores(cmaMesoTSScores);
            result.setBaseScores(baseScores);
            result.setSkillScores(skillScores);
            result.setFinalScore(finalScore);
            result.setTotalStations(actualData.size());
            result.setSuccess(true);
            result.setMessage("降水落区评分计算完成");

            log.info("降水落区评分计算完成，最终得分：{}", finalScore);
            return result;

        } catch (Exception e) {
            log.error("降水落区评分计算失败", e);
            PrecipitationScoringResult result = new PrecipitationScoringResult();
            result.setSuccess(false);
            result.setMessage("评分计算失败：" + e.getMessage());
            return result;
        }
    }

    /**
     * 解析实况降水数据（支持MICAPS第一类和第三类文件）
     */
    private List<StationPrecipitationData> parseActualPrecipitationData(String filePath) throws IOException {
        MicapsData micapsData = micapsDataService.parseMicapsFile(filePath);

        // 根据实际数据类型选择处理方式
        if (micapsData instanceof MicapsType1Data) {
            // 第一类数据：标准站点数据
            return parseActualFromType1Data((MicapsType1Data) micapsData);
        } else if (micapsData instanceof MicapsType3Data) {
            // 第三类数据：非规范站点填图数据
            return parseActualFromType3Data((MicapsType3Data) micapsData);
        } else {
            log.warn("不支持的实况数据类型: {}, 返回空数据", micapsData.getClass().getSimpleName());
            return new ArrayList<>();
        }
    }

    /**
     * 从第一类数据中解析实况降水数据
     */
    private List<StationPrecipitationData> parseActualFromType1Data(MicapsType1Data micapsData) {
        return micapsData.getStations().stream()
                .map(station -> {
                    StationPrecipitationData data = new StationPrecipitationData();
                    data.setStationId(station.getStationId());
                    data.setLongitude(station.getLongitude());
                    data.setLatitude(station.getLatitude());

                    // 从MICAPS站点数据中获取降水量
                    Double precipitation = station.getPrecipitation6h();
                    data.setActualPrecipitation(precipitation != null ? precipitation : 0.0);
                    data.setActualLevel(classifyPrecipitationLevel(data.getActualPrecipitation()));

                    return data;
                })
                .collect(Collectors.toList());
    }

    /**
     * 从第三类数据中解析实况降水数据
     */
    private List<StationPrecipitationData> parseActualFromType3Data(MicapsType3Data micapsData) {
        return micapsData.getStations().stream()
                .map(station -> {
                    StationPrecipitationData data = new StationPrecipitationData();
                    data.setStationId(station.getStationId());
                    data.setLongitude(station.getLongitude());
                    data.setLatitude(station.getLatitude());

                    // 从第三类站点数据中提取降水量
                    Double precipitation = extractPrecipitationFromType3Station(station);
                    data.setActualPrecipitation(precipitation != null ? precipitation : 0.0);
                    data.setActualLevel(classifyPrecipitationLevel(data.getActualPrecipitation()));

                    return data;
                })
                .collect(Collectors.toList());
    }

    /**
     * 解析CMA-MESO预报数据（支持MICAPS第三类和第四类文件）
     */
    private List<StationPrecipitationData> parseCmaMesoData(String filePath,
                                                           List<StationPrecipitationData> actualData) throws IOException {
        MicapsData micapsData = micapsDataService.parseMicapsFile(filePath);

        // 根据实际数据类型选择处理方式
        if (micapsData instanceof MicapsType4Data) {
            // 第四类数据：格点数据，需要插值
            return parseCmaMesoFromGridData((MicapsType4Data) micapsData, actualData);
        } else if (micapsData instanceof MicapsType3Data) {
            // 第三类数据：站点数据，直接匹配
            return parseCmaMesoFromStationData((MicapsType3Data) micapsData, actualData);
        } else if (micapsData instanceof MicapsType1Data) {
            // 第一类数据：站点数据，直接匹配
            return parseCmaMesoFromType1Data((MicapsType1Data) micapsData, actualData);
        } else {
            log.warn("不支持的MICAPS数据类型: {}, 返回空预报数据", micapsData.getClass().getSimpleName());
            return createEmptyForecastData(actualData);
        }
    }

    /**
     * 从第四类格点数据中解析CMA-MESO预报数据
     * 改进版本：添加详细的日志记录和数据验证
     */
    private List<StationPrecipitationData> parseCmaMesoFromGridData(MicapsType4Data gridData,
                                                                   List<StationPrecipitationData> actualData) {
        log.info("开始从第四类格点数据中插值获取站点预报数据");
        log.info("格点数据信息: {}×{} 格点，范围 [{:.3f}-{:.3f}°E, {:.3f}-{:.3f}°N]，间距 [{:.3f}°, {:.3f}°]",
            gridData.getLonGridNum(), gridData.getLatGridNum(),
            gridData.getStartLon(), gridData.getEndLon(),
            gridData.getMinLat(), gridData.getMaxLat(),
            gridData.getLonInterval(), gridData.getLatInterval());
        log.info("需要插值的站点数量: {}", actualData.size());

        int successCount = 0;
        int outOfRangeCount = 0;

        List<StationPrecipitationData> result = actualData.stream()
                .map(actualStation -> {
                    StationPrecipitationData data = new StationPrecipitationData();
                    data.setStationId(actualStation.getStationId());
                    data.setLongitude(actualStation.getLongitude());
                    data.setLatitude(actualStation.getLatitude());
                    data.setActualPrecipitation(actualStation.getActualPrecipitation());
                    data.setActualLevel(actualStation.getActualLevel());

                    // 从格点数据中插值获取该站点的预报降水量
                    Double forecastPrecipitation = interpolateGridValue(gridData,
                            actualStation.getLongitude(), actualStation.getLatitude());

                    if (forecastPrecipitation != null && forecastPrecipitation > 0) {
                        // 成功插值且有降水
                        data.setForecastPrecipitation(forecastPrecipitation);
                        log.debug("站点 {} ({:.3f}, {:.3f}) 插值成功: {:.2f}mm",
                            actualStation.getStationId(), actualStation.getLongitude(),
                            actualStation.getLatitude(), forecastPrecipitation);
                    } else {
                        // 插值失败或无降水
                        data.setForecastPrecipitation(0.0);
                        if (actualStation.getLongitude() < gridData.getStartLon() ||
                            actualStation.getLongitude() > gridData.getEndLon() ||
                            actualStation.getLatitude() < gridData.getMinLat() ||
                            actualStation.getLatitude() > gridData.getMaxLat()) {
                            log.debug("站点 {} ({:.3f}, {:.3f}) 超出格点数据范围",
                                actualStation.getStationId(), actualStation.getLongitude(),
                                actualStation.getLatitude());
                        }
                    }

                    data.setForecastLevel(classifyPrecipitationLevel(data.getForecastPrecipitation()));
                    return data;
                })
                .collect(Collectors.toList());

        // 统计插值结果
        successCount = (int) result.stream().filter(d -> d.getForecastPrecipitation() > 0).count();
        outOfRangeCount = actualData.size() - successCount;

        log.info("格点数据插值完成: 成功插值 {} 个站点，{} 个站点无降水或超出范围",
            successCount, outOfRangeCount);

        return result;
    }

    /**
     * 将格点数据插值到指定站点列表
     * 通用方法，可用于任何需要将格点数据转换为站点数据的场景
     *
     * @param gridData 格点数据
     * @param stations 站点列表（包含经纬度信息）
     * @return 插值后的站点数据映射 (stationId -> precipitation)
     */
    public Map<Long, Double> interpolateGridToStations(MicapsType4Data gridData,
                                                      List<StationPrecipitationData> stations) {
        if (gridData == null || stations == null || stations.isEmpty()) {
            log.warn("格点数据或站点列表为空，无法进行插值");
            return Collections.emptyMap();
        }

        log.info("开始将格点数据插值到 {} 个站点", stations.size());

        Map<Long, Double> result = new HashMap<>();
        int successCount = 0;

        for (StationPrecipitationData station : stations) {
            Double value = interpolateGridValue(gridData, station.getLongitude(), station.getLatitude());
            result.put(station.getStationId(), value != null ? value : 0.0);

            if (value != null && value > 0) {
                successCount++;
            }
        }

        log.info("格点插值完成: {} / {} 个站点有有效数据", successCount, stations.size());
        return result;
    }

    /**
     * 从第三类站点数据中解析CMA-MESO预报数据
     */
    private List<StationPrecipitationData> parseCmaMesoFromStationData(MicapsType3Data stationData,
                                                                      List<StationPrecipitationData> actualData) {
        // 创建站点ID到预报数据的映射
        Map<Long, MicapsType3Data.MicapsType3Station> forecastStationMap = stationData.getStations().stream()
                .collect(Collectors.toMap(
                    MicapsType3Data.MicapsType3Station::getStationId,
                    station -> station,
                    (existing, replacement) -> existing // 如果有重复ID，保留第一个
                ));

        return actualData.stream()
                .map(actualStation -> {
                    StationPrecipitationData data = new StationPrecipitationData();
                    data.setStationId(actualStation.getStationId());
                    data.setLongitude(actualStation.getLongitude());
                    data.setLatitude(actualStation.getLatitude());
                    data.setActualPrecipitation(actualStation.getActualPrecipitation());
                    data.setActualLevel(actualStation.getActualLevel());

                    // 查找对应的预报站点数据
                    MicapsType3Data.MicapsType3Station forecastStation = forecastStationMap.get(actualStation.getStationId());
                    if (forecastStation != null) {
                        // 尝试从站点值中获取降水量
                        Double forecastPrecipitation = extractPrecipitationFromType3Station(forecastStation);
                        data.setForecastPrecipitation(forecastPrecipitation != null ? forecastPrecipitation : 0.0);
                    } else {
                        // 如果没有找到对应站点，尝试通过距离最近的站点插值
                        Double forecastPrecipitation = findNearestStationType3Value(stationData,
                                actualStation.getLongitude(), actualStation.getLatitude());
                        data.setForecastPrecipitation(forecastPrecipitation != null ? forecastPrecipitation : 0.0);
                    }

                    data.setForecastLevel(classifyPrecipitationLevel(data.getForecastPrecipitation()));
                    return data;
                })
                .collect(Collectors.toList());
    }

    /**
     * 从第一类站点数据中解析CMA-MESO预报数据
     */
    private List<StationPrecipitationData> parseCmaMesoFromType1Data(MicapsType1Data stationData,
                                                                    List<StationPrecipitationData> actualData) {
        // 创建站点ID到预报数据的映射
        Map<Long, MicapsStation> forecastStationMap = stationData.getStations().stream()
                .collect(Collectors.toMap(
                    MicapsStation::getStationId,
                    station -> station,
                    (existing, replacement) -> existing // 如果有重复ID，保留第一个
                ));

        return actualData.stream()
                .map(actualStation -> {
                    StationPrecipitationData data = new StationPrecipitationData();
                    data.setStationId(actualStation.getStationId());
                    data.setLongitude(actualStation.getLongitude());
                    data.setLatitude(actualStation.getLatitude());
                    data.setActualPrecipitation(actualStation.getActualPrecipitation());
                    data.setActualLevel(actualStation.getActualLevel());

                    // 查找对应的预报站点数据
                    MicapsStation forecastStation = forecastStationMap.get(actualStation.getStationId());
                    if (forecastStation != null) {
                        // 从降水字段获取预报降水量
                        Double forecastPrecipitation = forecastStation.getPrecipitation6h();
                        data.setForecastPrecipitation(forecastPrecipitation != null ? forecastPrecipitation : 0.0);
                    } else {
                        // 如果没有找到对应站点，尝试通过距离最近的站点插值
                        Double forecastPrecipitation = findNearestStationType1Value(stationData,
                                actualStation.getLongitude(), actualStation.getLatitude());
                        data.setForecastPrecipitation(forecastPrecipitation != null ? forecastPrecipitation : 0.0);
                    }

                    data.setForecastLevel(classifyPrecipitationLevel(data.getForecastPrecipitation()));
                    return data;
                })
                .collect(Collectors.toList());
    }

    /**
     * 解析考生答案数据
     */
    private List<StationPrecipitationData> parseStudentAnswer(Map<String, Object> studentAnswer, 
                                                             List<StationPrecipitationData> actualData) {
        return actualData.stream()
                .map(actualStation -> {
                    StationPrecipitationData data = new StationPrecipitationData();
                    data.setStationId(actualStation.getStationId());
                    data.setLongitude(actualStation.getLongitude());
                    data.setLatitude(actualStation.getLatitude());
                    data.setActualPrecipitation(actualStation.getActualPrecipitation());
                    data.setActualLevel(actualStation.getActualLevel());
                    
                    // 判断该站点是否在考生绘制的降水落区内
                    String forecastLevel = determineStationForecastLevel(studentAnswer, 
                            actualStation.getLongitude(), actualStation.getLatitude());
                    data.setForecastLevel(forecastLevel);
                    data.setForecastPrecipitation(convertLevelToPrecipitation(forecastLevel));
                    
                    return data;
                })
                .collect(Collectors.toList());
    }

    /**
     * 创建空的预报数据（当无法解析预报文件时使用）
     */
    private List<StationPrecipitationData> createEmptyForecastData(List<StationPrecipitationData> actualData) {
        return actualData.stream()
                .map(actualStation -> {
                    StationPrecipitationData data = new StationPrecipitationData();
                    data.setStationId(actualStation.getStationId());
                    data.setLongitude(actualStation.getLongitude());
                    data.setLatitude(actualStation.getLatitude());
                    data.setActualPrecipitation(actualStation.getActualPrecipitation());
                    data.setActualLevel(actualStation.getActualLevel());
                    data.setForecastPrecipitation(0.0);
                    data.setForecastLevel("无降水");
                    return data;
                })
                .collect(Collectors.toList());
    }

    /**
     * 从第三类站点数据中提取降水量
     */
    private Double extractPrecipitationFromType3Station(MicapsType3Data.MicapsType3Station station) {
        // 尝试从value1获取降水量
        Double value1 = station.getNumericValue1();
        if (value1 != null && value1 >= 0) {
            return value1;
        }

        // 尝试从value2获取降水量
        Double value2 = station.getNumericValue2();
        if (value2 != null && value2 >= 0) {
            return value2;
        }

        return 0.0;
    }

    /**
     * 查找距离最近的第三类站点数值
     */
    private Double findNearestStationType3Value(MicapsType3Data stationData, double targetLon, double targetLat) {
        double minDistance = Double.MAX_VALUE;
        Double nearestValue = null;

        for (MicapsType3Data.MicapsType3Station station : stationData.getStations()) {
            double distance = calculateDistance(targetLon, targetLat,
                    station.getLongitude(), station.getLatitude());

            if (distance < minDistance) {
                Double value = extractPrecipitationFromType3Station(station);
                if (value != null) {
                    minDistance = distance;
                    nearestValue = value;
                }
            }
        }

        return nearestValue;
    }

    /**
     * 查找距离最近的第一类站点数值
     */
    private Double findNearestStationType1Value(MicapsType1Data stationData, double targetLon, double targetLat) {
        double minDistance = Double.MAX_VALUE;
        Double nearestValue = null;

        for (MicapsStation station : stationData.getStations()) {
            double distance = calculateDistance(targetLon, targetLat,
                    station.getLongitude(), station.getLatitude());

            if (distance < minDistance) {
                Double value = station.getPrecipitation6h();
                if (value != null) {
                    minDistance = distance;
                    nearestValue = value;
                }
            }
        }

        return nearestValue;
    }

    /**
     * 计算两点间的距离（简化的球面距离）
     */
    private double calculateDistance(double lon1, double lat1, double lon2, double lat2) {
        double deltaLon = lon2 - lon1;
        double deltaLat = lat2 - lat1;
        return Math.sqrt(deltaLon * deltaLon + deltaLat * deltaLat);
    }

    /**
     * 从格点数据中插值获取指定位置的数值
     * 改进版本：支持更好的错误处理和日志记录
     */
    private Double interpolateGridValue(MicapsType4Data gridData, double lon, double lat) {
        try {
            // 检查格点数据是否有效
            if (gridData == null) {
                log.warn("格点数据为空，无法进行插值");
                return 0.0;
            }

            if (gridData.getGridValues() == null || gridData.getGridValues().isEmpty()) {
                log.warn("格点数据值为空，无法进行插值");
                return 0.0;
            }

            // 检查位置是否在数据范围内
            if (lon < gridData.getStartLon() || lon > gridData.getEndLon() ||
                lat < gridData.getMinLat() || lat > gridData.getMaxLat()) {
                log.debug("位置({:.3f}, {:.3f})超出格点数据范围[{:.3f}-{:.3f}, {:.3f}-{:.3f}]",
                    lon, lat, gridData.getStartLon(), gridData.getEndLon(),
                    gridData.getMinLat(), gridData.getMaxLat());
                return 0.0;
            }

            // 使用MicapsType4Data内置的双线性插值方法
            Double value = gridData.getValueAtPosition(lon, lat);

            if (value != null) {
                log.debug("位置({:.3f}, {:.3f})插值结果: {:.2f}", lon, lat, value);
                return value;
            } else {
                log.debug("位置({:.3f}, {:.3f})插值返回null，使用默认值0.0", lon, lat);
                return 0.0;
            }

        } catch (Exception e) {
            log.warn("格点数据插值失败，位置：({:.3f}, {:.3f})，错误：{}", lon, lat, e.getMessage());
            return 0.0;
        }
    }

    /**
     * 判断站点在考生绘制的降水落区中的预报等级
     */
    private String determineStationForecastLevel(Map<String, Object> studentAnswer, double lon, double lat) {
        try {
            // 获取考生绘制的降水落区数据
            Map<String, Object> content = (Map<String, Object>) studentAnswer.get("content");
            if (content == null) {
                return "无雨";
            }

            // 按降水等级优先级检查（从强到弱）
            String[] levels = {"大暴雨", "暴雨", "大雨", "中雨", "小雨"};
            
            for (String level : levels) {
                if (content.containsKey(level)) {
                    List<Map<String, Object>> areas = (List<Map<String, Object>>) content.get(level);
                    if (areas != null && !areas.isEmpty()) {
                        for (Map<String, Object> area : areas) {
                            if (isPointInArea(lon, lat, area)) {
                                return level;
                            }
                        }
                    }
                }
            }
            
            return "无雨";
        } catch (Exception e) {
            log.warn("判断站点降水等级失败，位置：({}, {})，错误：{}", lon, lat, e.getMessage());
            return "无雨";
        }
    }

    /**
     * 判断点是否在区域内（使用射线法）
     */
    private boolean isPointInArea(double lon, double lat, Map<String, Object> area) {
        try {
            Map<String, Object> geometry = (Map<String, Object>) area.get("geometry");
            if (geometry == null || !"Polygon".equals(geometry.get("type"))) {
                return false;
            }

            List<List<List<Double>>> coordinates = (List<List<List<Double>>>) geometry.get("coordinates");
            if (coordinates == null || coordinates.isEmpty()) {
                return false;
            }

            List<List<Double>> polygon = coordinates.get(0); // 取外环
            return isPointInPolygon(lon, lat, polygon);
        } catch (Exception e) {
            log.warn("判断点是否在区域内失败：{}", e.getMessage());
            return false;
        }
    }

    /**
     * 使用射线法判断点是否在多边形内
     */
    private boolean isPointInPolygon(double lon, double lat, List<List<Double>> polygon) {
        int intersections = 0;
        int n = polygon.size();
        
        for (int i = 0; i < n - 1; i++) {
            List<Double> p1 = polygon.get(i);
            List<Double> p2 = polygon.get(i + 1);
            
            double x1 = p1.get(0), y1 = p1.get(1);
            double x2 = p2.get(0), y2 = p2.get(1);
            
            // 检查射线是否与边相交
            if (((y1 > lat) != (y2 > lat)) && 
                (lon < (x2 - x1) * (lat - y1) / (y2 - y1) + x1)) {
                intersections++;
            }
        }
        
        return (intersections % 2) == 1;
    }

    /**
     * 降水等级分类
     */
    private String classifyPrecipitationLevel(double precipitation) {
        if (precipitation < 0.1) {
            return "无雨";
        } else if (precipitation < 10.0) {
            return "小雨";
        } else if (precipitation < 25.0) {
            return "中雨";
        } else if (precipitation < 50.0) {
            return "大雨";
        } else if (precipitation < 100.0) {
            return "暴雨";
        } else {
            return "大暴雨";
        }
    }

    /**
     * 将降水等级转换为降水量（用于统计）
     */
    private double convertLevelToPrecipitation(String level) {
        switch (level) {
            case "小雨": return 5.0;
            case "中雨": return 17.5;
            case "大雨": return 37.5;
            case "暴雨": return 75.0;
            case "大暴雨": return 150.0;
            default: return 0.0;
        }
    }

    /**
     * 计算所有量级的TS评分
     */
    private Map<String, Double> calculateAllLevelTS(List<StationPrecipitationData> forecastData, 
                                                   List<StationPrecipitationData> actualData) {
        Map<String, Double> tsScores = new HashMap<>();
        
        // 晴雨TS评分
        tsScores.put("晴雨", calculateRainNoRainTS(forecastData, actualData));
        
        // 各量级TS评分
        tsScores.put("小雨", calculateLevelTS(forecastData, actualData, "小雨"));
        tsScores.put("中雨", calculateLevelTS(forecastData, actualData, "中雨"));
        tsScores.put("大雨", calculateLevelTS(forecastData, actualData, "大雨"));
        tsScores.put("暴雨", calculateLevelTS(forecastData, actualData, "暴雨"));
        tsScores.put("大暴雨", calculateLevelTS(forecastData, actualData, "大暴雨"));
        
        return tsScores;
    }

    /**
     * 计算晴雨TS评分
     */
    private double calculateRainNoRainTS(List<StationPrecipitationData> forecastData, 
                                        List<StationPrecipitationData> actualData) {
        int A = 0, B = 0, C = 0, D = 0;
        
        for (int i = 0; i < actualData.size(); i++) {
            StationPrecipitationData actual = actualData.get(i);
            StationPrecipitationData forecast = forecastData.get(i);
            
            boolean actualRain = actual.getActualPrecipitation() >= 0.1;
            boolean forecastRain = forecast.getForecastPrecipitation() >= 0.1;
            
            if (forecastRain && actualRain) {
                A++; // 正确预报有降水
            } else if (forecastRain && !actualRain) {
                B++; // 空报
            } else if (!forecastRain && actualRain) {
                C++; // 漏报
            } else {
                D++; // 正确预报无降水
            }
        }
        
        int total = A + B + C + D;
        return total > 0 ? (double)(A + D) / total : 0.0;
    }

    /**
     * 计算指定量级的TS评分
     */
    private double calculateLevelTS(List<StationPrecipitationData> forecastData, 
                                   List<StationPrecipitationData> actualData, 
                                   String level) {
        int A = 0, B = 0, C = 0;
        
        for (int i = 0; i < actualData.size(); i++) {
            StationPrecipitationData actual = actualData.get(i);
            StationPrecipitationData forecast = forecastData.get(i);
            
            boolean actualInLevel = level.equals(actual.getActualLevel());
            boolean forecastInLevel = level.equals(forecast.getForecastLevel());
            
            if (actualInLevel) {
                if (forecastInLevel) {
                    A++; // 正确预报该量级
                } else if (!"无雨".equals(forecast.getForecastLevel())) {
                    B++; // 预报为其他量级
                } else {
                    C++; // 漏报（预报无雨）
                }
            }
        }
        
        int total = A + B + C;
        return total > 0 ? (double)A / total : 0.0;
    }

    /**
     * 计算基础分
     */
    private Map<String, Double> calculateBaseScores(Map<String, Double> studentTS, 
                                                   Map<String, Double> cmaMesoTS) {
        Map<String, Double> baseScores = new HashMap<>();
        
        for (String level : studentTS.keySet()) {
            double studentScore = studentTS.get(level);
            double cmaScore = cmaMesoTS.getOrDefault(level, 0.0);
            
            baseScores.put(level, studentScore >= cmaScore ? 0.3 : 0.0);
        }
        
        return baseScores;
    }

    /**
     * 计算技巧评分
     */
    private Map<String, Double> calculateSkillScores(Map<String, Double> studentTS, 
                                                     Map<String, Double> baseScores) {
        Map<String, Double> skillScores = new HashMap<>();
        
        for (String level : studentTS.keySet()) {
            double baseScore = baseScores.get(level);
            double tsScore = studentTS.get(level);
            
            skillScores.put(level, baseScore + tsScore * 0.7);
        }
        
        return skillScores;
    }

    /**
     * 计算加权最终评分
     */
    private double calculateWeightedFinalScore(Map<String, Double> skillScores) {
        Map<String, Double> weights = new HashMap<>();
        weights.put("晴雨", 0.1);
        weights.put("小雨", 0.2);
        weights.put("中雨", 0.2);
        weights.put("大雨", 0.2);
        weights.put("暴雨", 0.2);
        weights.put("大暴雨", 0.1);
        
        double weightedSum = 0.0;
        for (String level : skillScores.keySet()) {
            double skillScore = skillScores.get(level);
            double weight = weights.getOrDefault(level, 0.0);
            weightedSum += skillScore * weight;
        }
        
        return weightedSum * 40.0; // 乘以总分40分
    }

    /**
     * 根据区域编码筛选站点数据
     *
     * @param actualData 原始站点数据
     * @param regionCode 区域编码（1-9）
     * @return 筛选后的站点数据
     */
    private List<StationPrecipitationData> filterStationsByRegion(List<StationPrecipitationData> actualData, String regionCode) {
        try {
            // 创建区域查询请求DTO
            ElStationRegionReqDTO reqDTO = new ElStationRegionReqDTO();
            reqDTO.setRegionCode(regionCode);

            // 设置站点等级列表（国家站、基准站、基本站）
            reqDTO.setStationLevls(Arrays.asList("11", "12", "13"));

            // 获取该区域的所有站点
            List<ElStationRespDTO> regionStations = stationService.listByRegionCodes(reqDTO);
            if (regionStations.isEmpty()) {
                log.warn("区域编码{}没有找到对应的站点", regionCode);
                return actualData; // 如果没有找到区域站点，返回原始数据
            }

            // 创建站点ID集合用于快速查找
            Set<String> regionStationIds = regionStations.stream()
                .map(ElStationRespDTO::getStationIdC)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

            // 筛选属于该区域的站点数据
            List<StationPrecipitationData> filteredData = actualData.stream()
                .filter(data -> regionStationIds.contains(String.valueOf(data.getStationId())))
                .collect(Collectors.toList());

            log.info("区域{}包含{}个站点，实况数据{}个站点，筛选出{}个有效站点数据",
                regionCode, regionStations.size(), actualData.size(), filteredData.size());
            return filteredData;

        } catch (Exception e) {
            log.error("根据区域编码筛选站点数据失败，区域编码：{}", regionCode, e);
            return actualData; // 出错时返回原始数据
        }
    }
}
